import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/about',
      name: 'about',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/AboutView.vue'),
    },
    {
      path: '/test',
      name: 'test',
      component: () => import('../tests/FieldEdgeTest.vue'),
    },
    {
      path: '/interaction-test',
      name: 'interaction-test',
      component: () => import('../tests/InteractionTest.vue'),
    },
    {
      path: '/field-interaction-test',
      name: 'field-interaction-test',
      component: () => import('../tests/FieldInteractionTest.vue'),
    },
  ],
})

export default router
